import * as arrowData from '@/assets/lottie/arrow-right.json'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { zodResolver } from '@hookform/resolvers/zod'
import dynamic from 'next/dynamic'
import { useForm } from 'react-hook-form'

import { z } from 'zod'
const Lottie = dynamic(() => import('react-lottie-player'), { ssr: false })

import { createDefaultValues, createDynamicSchema } from '@/app/(panel)/banks/connect/common/helper'
import { Icons } from '@/components/icons'
import type { BankConfig, BankConnectionField } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { useMemo } from 'react'
import { toast } from 'sonner'
import BankIntroduction from '../../components/bank-introduction'
import BankNote from '../../components/bank-note'

export default function Component({ bankCode, bank }: { bankCode: string; bank: BankConfig | null }) {
  const { isPending, mutate } = useMutation({
    mutationFn: (data: z.infer<typeof dynamicSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/${bankCode}/check-account`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      toast.error(error.message)
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success('Kết nối thành công')
        // setIsShowRegisterForm(false);
      } else {
        toast.error(data.message)
      }
    },
  })

  const bankRequiredFields = useMemo<BankConnectionField[]>(() => {
    if (!bank) return []
    return bank.connection_fields_config || []
  }, [bank])

  const dynamicSchema = useMemo(() => createDynamicSchema(bankRequiredFields), [bankRequiredFields])

  const form = useForm<z.infer<typeof dynamicSchema>>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: createDefaultValues(bankRequiredFields),
  })

  // const onSubmit = (values: Record<BankFieldRequirement['name'], string>) => {
  const onSubmit = (values: z.infer<typeof dynamicSchema>) => {
    mutate(values)
  }

  return (
    <>
      <BankIntroduction bank={bank} />
      <div className="text-center">Vui lòng nhập thông tin kết nối để tiếp tục</div>
      <Card className="border-0">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="**:data-[error=true]:text-primary space-y-8 p-4">
            {bankRequiredFields &&
              bankRequiredFields.length > 0 &&
              bankRequiredFields.map((bankField) => (
                <FormField
                  key={bankField.name}
                  control={form.control}
                  name={bankField.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{bankField.title}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormDescription>{bankField.description}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            <Button
              type="submit"
              size="sm"
              className="w-full gap-2"
              disabled={isPending}>
              {isPending && <Icons.spinner className="animate-spin" />}
              Tiếp tục
              <Lottie
                className="dark:invert"
                play
                animationData={arrowData}
                style={{ width: 20, height: 20 }}
              />
            </Button>
          </form>
        </Form>
      </Card>
      <BankNote bank={bank} />
    </>
  )
}

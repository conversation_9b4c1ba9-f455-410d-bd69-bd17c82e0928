import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'

import { z } from 'zod'

import { createDynamicSchema } from '@/app/(panel)/banks/connect/common/helper'
import {
  DescriptionDetail,
  DescriptionGroup,
  DescriptionList,
  DescriptionTerm,
} from '@/components/custom-ui/description-list'
import { Icons } from '@/components/icons'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardFooter, CardHeader } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import type { BankConnectionField } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import confetti from 'canvas-confetti'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { FaCircleCheck } from 'react-icons/fa6'
import { IoCopyOutline } from 'react-icons/io5'
import { TbConfetti } from 'react-icons/tb'
import { toast } from 'sonner'
import { useStore } from '../../stores/store'

const formSchema = z.object({
  accountNumber: z.string().min(1, {
    message: 'Số tài khoản không được để trống.',
  }),
  cccd: z.string().min(9, {
    message: 'Số CCCD/CMT phải tối thiểu là 9 ký tự.',
  }),
  phone: z.string().min(9, {
    message: 'Số điện thoại phải tối thiểu là 9 ký tự.',
  }),
})

import { ScrollArea } from '@/components/ui/scroll-area'
import { useCountdown } from 'usehooks-ts'

export default function Component() {
  const { bankCode, setIsShowRegisterForm, isShowGreeting, setIsShowGreeting } = useStore()

  const [received, setReceived] = useState(false)
  const router = useRouter()

  const { isPending, mutate } = useMutation({
    mutationFn: (data: z.infer<typeof dynamicSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/${bankCode}/${data.accountNumber}/check`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      toast.error(error.message)
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(data.message ?? 'Kết nối thành công')
        // setIsShowRegisterForm(false);
      } else {
        toast.error(data.message)
      }
    },
  })

  const bank = banks.find((bank) => bank.code === bankCode)

  const bankRequiredFields = useMemo<BankFieldRequirement[]>(
    () =>
      connectionFields.filter((connectionField) => {
        return bank?.connectionFields.includes(connectionField.name)
      }),
    [bank]
  )

  const dynamicSchema = useMemo(() => createDynamicSchema(bankRequiredFields), [bankRequiredFields])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      accountNumber: '',
      cccd: '',
      phone: '',
    },
  })

  // const onSubmit = (values: Record<BankFieldRequirement['name'], string>) => {
  const onSubmit = (values: z.infer<typeof dynamicSchema>) => {
    mutate(values)
  }

  useEffect(() => {
    if (!isShowGreeting) return

    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
    })

    const timeoutId = setTimeout(() => {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
      })
      setReceived(true)
      // setIsShowRegisterForm(false);
    }, 5000)

    return () => clearTimeout(timeoutId)
  }, [isShowGreeting])

  const closeDialog = useCallback(() => {
    setIsShowGreeting(false)
    setIsShowRegisterForm(false)
  }, [setIsShowGreeting, setIsShowRegisterForm])

  const [count, { startCountdown }] = useCountdown({
    countStart: 10,
    intervalMs: 1000,
  })

  useEffect(() => {
    if (received) {
      startCountdown()
    }

    if (count === 0) {
      closeDialog()
      router.push('/banks')
    }
  }, [received, count, closeDialog, startCountdown, router])

  return (
    <>
      <Dialog
        open={isShowGreeting}
        onOpenChange={() => closeDialog()}>
        <DialogContent
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
          className="gap-6 sm:max-w-3xl">
          <DialogHeader className="text-left">
            <DialogTitle className="flex items-center gap-2">
              <TbConfetti className="fill-warning size-6" />
              Xin chúc mừng!
            </DialogTitle>
            <DialogDescription className="space-x-1">Tài khoản của bạn đã được khởi tạo thành công.</DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-[75vh] md:h-auto">
            <DialogFooter className="flex-col gap-4">
              <DescriptionList className="divide-y md:w-2/3">
                <DescriptionGroup>
                  <DescriptionTerm className="">Số tài khoản (VA)</DescriptionTerm>
                  <DescriptionDetail className="flex items-start justify-between">
                    ITGCODETAY
                    <IoCopyOutline
                      data-tooltip-html="Sao chép"
                      className="cursor-pointer"
                    />
                  </DescriptionDetail>
                </DescriptionGroup>

                <DescriptionGroup>
                  <DescriptionTerm className="">Số tài khoản chính</DescriptionTerm>
                  <DescriptionDetail className="flex items-start justify-between">
                    0988999755
                    <IoCopyOutline
                      data-tooltip-html="Sao chép"
                      className="cursor-pointer"
                    />
                  </DescriptionDetail>
                </DescriptionGroup>

                <DescriptionGroup>
                  <DescriptionTerm>Tên tài khoản</DescriptionTerm>
                  <DescriptionDetail className="flex items-start justify-between">
                    Lương Văn Đức
                    <IoCopyOutline
                      data-tooltip-html="Sao chép"
                      className="cursor-pointer"
                    />
                  </DescriptionDetail>
                </DescriptionGroup>

                <DescriptionGroup>
                  <DescriptionTerm className="">Ngân hàng</DescriptionTerm>
                  <DescriptionDetail>OceanBank (OCB)</DescriptionDetail>
                </DescriptionGroup>

                <DescriptionGroup>
                  <DescriptionTerm className="">Ngày khởi tạo</DescriptionTerm>
                  <DescriptionDetail>09/03/2025 12:34</DescriptionDetail>
                </DescriptionGroup>
                <DescriptionGroup>
                  <DescriptionTerm className="">Trạng thái</DescriptionTerm>
                  <DescriptionDetail>
                    <Badge
                      variant="outline"
                      className="text-muted-foreground px-1.5">
                      <FaCircleCheck className="fill-green-500 dark:fill-green-400" />
                      Kích hoạt
                    </Badge>
                  </DescriptionDetail>
                </DescriptionGroup>
              </DescriptionList>
              <Card className="md:w-2/3">
                <CardHeader>
                  <CardDescription className="flex justify-center">
                    {received ? (
                      <div className="flex flex-col items-center justify-center gap-2">
                        <FaCircleCheck className="size-10 fill-green-500 dark:fill-green-400" />
                        Giao dịch thành công!
                      </div>
                    ) : (
                      <>
                        Để kiểm thử tài khoản, bạn có thể thực hiện chuyển khoản với số tiền bất kỳ đến tài khoản này
                        bằng cách quét mã QR bên dưới.
                      </>
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center">
                  {received ? (
                    <DescriptionList className="w-full gap-1 divide-y px-5">
                      <DescriptionGroup className="flex items-center justify-between py-2">
                        <DescriptionTerm className="">Số tiền</DescriptionTerm>
                        <DescriptionDetail>100.000đ</DescriptionDetail>
                      </DescriptionGroup>

                      <DescriptionGroup className="flex items-center justify-between py-2">
                        <DescriptionTerm>Tài khoản</DescriptionTerm>
                        <DescriptionDetail>ITGCODETAY</DescriptionDetail>
                      </DescriptionGroup>

                      <DescriptionGroup className="flex items-center justify-between py-2">
                        <DescriptionTerm>Nội dung</DescriptionTerm>
                        <DescriptionDetail>Donate culi grabber</DescriptionDetail>
                      </DescriptionGroup>

                      <DescriptionGroup className="flex items-center justify-between py-2">
                        <DescriptionTerm>Thời gian</DescriptionTerm>
                        <DescriptionDetail>09/04/2025 18:51</DescriptionDetail>
                      </DescriptionGroup>
                    </DescriptionList>
                  ) : (
                    <div className="space-y-3">
                      <Image
                        src="https://vietqr.co/api/generate/ocb/0988999755/VIETQR.CO?style=2"
                        alt="qrcode"
                        width={300}
                        height={500}
                      />
                      <div className="text-muted-foreground flex items-center justify-center gap-1 text-xs">
                        <Icons.spinner className="size-4 animate-spin" /> Đang chờ giao dịch...
                      </div>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="text-muted-foreground text-center">
                  Bây giờ bạn đã có thể sử dụng tài khoản để nhận tiền và tích hợp các dịch vụ của hệ thống.
                </CardFooter>
              </Card>
            </DialogFooter>
          </ScrollArea>
          <Separator />
          <div
            className="text-muted-foreground space-x-1 text-center"
            dangerouslySetInnerHTML={{
              __html: received
                ? count > 0
                  ? `Đang chuyển trang trong ${count} giây...`
                  : 'Đang chuyển trang...'
                : 'Kiểm thử kết nối là <strong>không bắt buộc</strong> và bạn có thể bỏ qua bước này để tiếp tục sử dụng hệ thống.',
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  )
}

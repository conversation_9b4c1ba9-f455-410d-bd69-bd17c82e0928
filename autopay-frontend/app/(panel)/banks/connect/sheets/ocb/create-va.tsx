import { Button } from '@/components/ui/button'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'

import { z } from 'zod'

import { createDynamicSchema } from '@/app/(panel)/banks/connect/common/helper'
import {
  InputBase,
  InputBaseAdornment,
  InputBaseAdornmentButton,
  InputBaseControl,
  InputBaseInput,
} from '@/components/custom-ui/input-base'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import type { BankConnectionField } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { useMemo } from 'react'
import { IoIosSend } from 'react-icons/io'
import { MdOutlineAutoFixHigh } from 'react-icons/md'
import { VscAccount } from 'react-icons/vsc'
import { toast } from 'sonner'
import { useStore } from '../../stores/store'

const formSchema = z.object({
  accountNumber: z.string().min(1, {
    message: 'Số tài khoản không được để trống.',
  }),
  cccd: z.string().min(9, {
    message: 'Số CCCD/CMT phải tối thiểu là 9 ký tự.',
  }),
  phone: z.string().min(9, {
    message: 'Số điện thoại phải tối thiểu là 9 ký tự.',
  }),
})

export default function Component() {
  const { bankCode, bankAccount, isShowCreateVaForm, setIsShowCreateVaForm, setIsShowOtp } = useStore()

  const { isPending, mutate } = useMutation({
    mutationFn: (data: z.infer<typeof dynamicSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/${bankCode}/${bankAccount.accountNumber}/check`, {
        method: 'POST',
        body: JSON.stringify(bankAccount),
      }),
    onError: (error) => {
      toast.error(error.message)
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(data.message ?? 'Kết nối thành công')
        setIsShowCreateVaForm(!isShowCreateVaForm)
        setIsShowOtp(true)
      } else {
        toast.error(data.message)
      }
    },
  })

  const bank = banks.find((bank) => bank.code === bankCode)

  const bankRequiredFields = useMemo<BankFieldRequirement[]>(
    () =>
      connectionFields.filter((connectionField) => {
        return bank?.connectionFields.includes(connectionField.name)
      }),
    [bank]
  )

  const dynamicSchema = useMemo(() => createDynamicSchema(bankRequiredFields), [bankRequiredFields])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      accountNumber: '',
      cccd: '',
      phone: '',
    },
  })

  // const onSubmit = (values: Record<BankFieldRequirement['name'], string>) => {
  const onSubmit = (values: z.infer<typeof dynamicSchema>) => {
    console.log(values, bankAccount)
    mutate(values)
  }

  return (
    <>
      <Dialog
        open={isShowCreateVaForm}
        onOpenChange={() => setIsShowCreateVaForm(!isShowCreateVaForm)}>
        <DialogContent
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
          className="sm:max-w-fit">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <VscAccount />
              Khởi tạo tài khoản ảo (VA)
            </DialogTitle>
            <DialogDescription>
              Ngân hàng OCB hiện chỉ hỗ trợ thông báo cho các giao dịch phát sinh trên tài khoản ảo.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="**:data-[error=true]:text-primary space-y-8 py-4">
              <FormField
                control={form.control}
                name="accountNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tài khoản ảo (VA)</FormLabel>
                    <FormControl>
                      {/*<Input {...field} />*/}
                      <InputBase>
                        <InputBaseAdornment
                          className="bg-accent pointer-events-auto z-20 cursor-help rounded px-1.5 font-semibold"
                          data-tooltip-html={`Tiền tố của tài khoản ảo sẽ bắt đầu bằng mã đại diện "${bank?.virtualAccountPrefix}"`}>
                          {bank?.virtualAccountPrefix}
                        </InputBaseAdornment>
                        <InputBaseControl>
                          <InputBaseInput
                            type="text"
                            placeholder="CODETAY"
                            {...field}
                            data-tooltip-html={`Tên tài khoản ảo tối thiểu là 01 ký tự và tối đa là 15 ký tự.<br/>Sử dụng các chữ cái IN HOA viết liền không dấu và không chứa ký tự đặc biệt`}
                          />
                        </InputBaseControl>
                        <InputBaseAdornment>
                          <InputBaseAdornmentButton
                            asChild
                            data-tooltip-html="Sinh tên tự động"
                            className="cursor-pointer">
                            <MdOutlineAutoFixHigh />
                          </InputBaseAdornmentButton>
                        </InputBaseAdornment>
                      </InputBase>
                    </FormControl>
                    <FormDescription>
                      Tài khoản ảo thường sử dụng để đại diện cho một chi nhánh cửa hàng.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="cccd"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa chỉ</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      Địa chỉ là địa chỉ của bạn (cửa hàng/chi nhánh), không phải địa chỉ của ngân hàng.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên gợi nhớ</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      Tên gợi nhớ là tên bạn muốn hiển thị để dễ nhận biết trên hệ thống.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>

          <DialogFooter className="sm:justify-center">
            <Button
              type="submit"
              size="sm"
              onClick={() => form.handleSubmit(onSubmit)()}>
              <IoIosSend />
              Lấy mã OTP
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

import * as arrowData from '@/assets/lottie/arrow-right.json'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { zodResolver } from '@hookform/resolvers/zod'
import dynamic from 'next/dynamic'
import { Popover as PopoverRadix } from 'radix-ui'
import { useForm } from 'react-hook-form'
import { MdError } from 'react-icons/md'
import { z } from 'zod'
const Lottie = dynamic(() => import('react-lottie-player'), { ssr: false })

import { createDefaultValues, createDynamicSchema } from '@/app/(panel)/banks/connect/common/helper'
import BankIntroduction from '@/app/(panel)/banks/connect/components/bank-introduction'
import { Icons } from '@/components/icons'
import { Alert, AlertDescription } from '@/components/ui/alert'
import type { BankConfig, BankConnectionField } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { RefObject, useMemo, useRef } from 'react'
import { toast } from 'sonner'
import { useOnClickOutside } from 'usehooks-ts'
import BankNote from '../../components/bank-note'
import { useStore } from '../../stores/store'
import CreateVa from './create-va'
import Greeting from './greeting'
import Otp from './otp'

export default function Component({ bankCode, bank }: { bankCode: string; bank: BankConfig | null }) {
  const { setIsShowCreateVaForm, setBankAccount } = useStore()

  const {
    isPending,
    mutate: checkBankAccount,
    isError: isErrorMutation,
    error,
    reset: resetMutationState,
  } = useMutation({
    mutationFn: (data: z.infer<typeof dynamicSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/${bankCode}/${data.accountNumber}/check`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      // toast.error(error.message, {
      //   style: {
      //     width: 'fit-content',
      //     whiteSpace: 'nowrap',
      //   },
      // });
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(data.message ?? 'Kết nối thành công')
        setBankAccount(getAccountData())
        setIsShowCreateVaForm(true)
      } else {
        toast.error(data.message)
      }
    },
  })

  const bankRequiredFields = useMemo<BankConnectionField[]>(() => {
    if (!bank) return []
    return bank.connection_fields_config || []
  }, [bank])

  const dynamicSchema = useMemo(() => createDynamicSchema(bankRequiredFields), [bankRequiredFields])

  const form = useForm<z.infer<typeof dynamicSchema>>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: createDefaultValues(bankRequiredFields),
  })

  // const onSubmit = (values: Record<BankFieldRequirement['name'], string>) => {
  const onSubmit = (values: z.infer<typeof dynamicSchema>) => {
    checkBankAccount(values)
  }

  const getAccountData = () => {
    const values = form.getValues()
    return {
      phoneNumber: values.phoneNumber ?? '',
      accountNumber: values.accountNumber ?? '',
      idCardNumber: values.idCardNumber ?? '',
    }
  }

  const refPopover = useRef<HTMLDivElement>(null)

  useOnClickOutside(refPopover as RefObject<HTMLElement>, () => {
    resetMutationState()
  })

  return (
    <>
      <BankIntroduction bank={bank} />
      <div className="text-center">Vui lòng nhập thông tin kết nối để tiếp tục</div>
      <Card className="border-0">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="**:data-[error=true]:text-primary space-y-8 p-4">
            {bankRequiredFields &&
              bankRequiredFields.length > 0 &&
              bankRequiredFields.map((bankField) => (
                <FormField
                  key={bankField.name}
                  control={form.control}
                  name={bankField.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{bankField.title}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormDescription>{bankField.description}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            <Popover open={isErrorMutation}>
              <PopoverTrigger asChild>
                <Button
                  type="submit"
                  size="sm"
                  className="w-full gap-2"
                  disabled={isPending}>
                  {isPending && <Icons.spinner className="animate-spin" />}
                  Tiếp tục
                  <Lottie
                    className="dark:invert"
                    play
                    animationData={arrowData}
                    style={{ width: 20, height: 20 }}
                  />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                ref={refPopover}
                side="top"
                className="w-fit border-0 p-0">
                <Alert variant="destructive">
                  <MdError />
                  <AlertDescription>{error?.message ?? 'Có lỗi xảy ra, vui lòng thử lại sau'}</AlertDescription>
                </Alert>
                <PopoverRadix.Arrow className="fill-accent" />
              </PopoverContent>
            </Popover>
          </form>
        </Form>
      </Card>
      <BankNote bank={bank} />
      <CreateVa />
      <Otp />
      <Greeting />
    </>
  )
}

import { Button } from '@/components/ui/button'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'

import { BiSolidBadgeCheck } from 'react-icons/bi'
import { z } from 'zod'

import { createDynamicSchema } from '@/app/(panel)/banks/connect/common/helper'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp'
import { Separator } from '@/components/ui/separator'
import type { BankConnectionField } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { GrConnect } from 'react-icons/gr'
import { toast } from 'sonner'
import { useStore } from '../../stores/store'

const formSchema = z.object({
  accountNumber: z.string().min(1, {
    message: '<PERSON><PERSON> tài khoản không được để trống.',
  }),
  cccd: z.string().min(9, {
    message: 'Số CCCD/CMT phải tối thiểu là 9 ký tự.',
  }),
  phone: z.string().min(9, {
    message: 'Số điện thoại phải tối thiểu là 9 ký tự.',
  }),
})

export default function Component() {
  const { bankCode, bankAccount, isShowOtp, setIsShowOtp, setIsShowGreeting } = useStore()
  const [seconds, setSeconds] = useState(60)

  const { isPending, mutate } = useMutation({
    mutationFn: (data: z.infer<typeof dynamicSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/${bankCode}/${data.accountNumber}/check`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      toast.error(error.message)
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(data.message ?? 'Kết nối thành công')
        setIsShowOtp(false)
        setIsShowGreeting(true)
      } else {
        toast.error(data.message)
      }
    },
  })

  const bank = banks.find((bank) => bank.code === bankCode)

  const bankRequiredFields = useMemo<BankFieldRequirement[]>(
    () =>
      connectionFields.filter((connectionField) => {
        return bank?.connectionFields.includes(connectionField.name)
      }),
    [bank]
  )

  const dynamicSchema = useMemo(() => createDynamicSchema(bankRequiredFields), [bankRequiredFields])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      accountNumber: '',
      cccd: '',
      phone: '',
    },
  })

  // const onSubmit = (values: Record<BankFieldRequirement['name'], string>) => {
  const onSubmit = (values: z.infer<typeof dynamicSchema>) => {
    mutate(values)
  }

  const countdown = useCallback(() => {
    const interval = setInterval(() => {
      setSeconds((prev) => {
        if (prev <= 0) {
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    // Cleanup interval when component unmounts or when countdown is called again
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    if (!isShowOtp) return

    // Start countdown when the dialog opens
    // a Cleanup interval when the dialog closes or component unmounts
    return countdown()
  }, [isShowOtp, countdown])

  const closeDialog = () => {
    setIsShowOtp(!isShowOtp)
    setSeconds(120)
  }

  return (
    <>
      <Dialog
        open={isShowOtp}
        onOpenChange={() => closeDialog()}>
        <DialogContent
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
          className="gap-6 sm:max-w-xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <GrConnect />
              Xác nhận kết nối
            </DialogTitle>
            <DialogDescription className="space-x-1">
              Mã OTP đã được gửi đến số điện thoại <strong>{bankAccount.phoneNumber}</strong>
              đã đăng ký với ngân hàng.
              <br />
              Vui lòng nhập mã OTP để xác nhận kết nối tài khoản với hệ thống.
            </DialogDescription>
          </DialogHeader>

          <InputOTP
            maxLength={6}
            containerClassName="justify-center">
            <InputOTPGroup>
              <InputOTPSlot index={0} />
              <InputOTPSlot index={1} />
              <InputOTPSlot index={2} />
              <InputOTPSlot index={3} />
              <InputOTPSlot index={4} />
              <InputOTPSlot index={5} />
            </InputOTPGroup>
          </InputOTP>

          <DialogFooter className="items-center gap-2 sm:flex-col sm:justify-center">
            <div className="flex items-center gap-1">
              <Button
                type="submit"
                size="sm"
                className="w-fit"
                onClick={() => mutate(bankAccount)}>
                <BiSolidBadgeCheck />
                Xác nhận
              </Button>
              <Button
                size="sm"
                className="text-muted-foreground w-fit"
                variant="ghost"
                disabled={seconds > 0}
                onClick={() => {
                  setSeconds(60)
                  countdown() // Start new countdown when "Gửi lại mã" is clicked
                }}>
                Gửi lại mã? {seconds > 0 ? `(${seconds}s)` : ''}
              </Button>
            </div>
          </DialogFooter>
          <Separator />
          <div className="text-muted-foreground space-x-1 text-center">
            Trong trường hợp bạn không nhận được mã OTP, hoặc mã đã hết hiệu lực.
            <br />
            Vui lòng kiểm tra lại số điện thoại hoặc nhấn gửi lại mã.
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

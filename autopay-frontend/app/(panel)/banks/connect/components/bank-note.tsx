import { Alert, AlertDescription } from '@/components/ui/alert'
import type { BankConfig } from '@/lib/types/banks'
import ReactMarkdown from 'react-markdown'

export default function Component({ bank }: { bank: BankConfig | null }) {
  if (!bank) {
    return null
  }

  return (
    <Alert className="bg-accent/10">
      <AlertDescription className="text-muted-foreground flex gap-4">
        <ul>
          {bank?.ui_config.notes.map((note: string) => (
            <li key={note}>
              <ReactMarkdown
                components={{
                  ul: ({ children }) => <>{children}</>,
                  li(props) {
                    const { children } = props
                    return (
                      <div className={children ? '' : 'h-2'}>
                        {children ? '-' : null} {children}
                      </div>
                    )
                  },
                  a(props) {
                    const { children, ...rest } = props
                    return (
                      <a
                        {...rest}
                        className="text-blue-700">
                        {children}
                      </a>
                    )
                  },
                }}>
                {note}
              </ReactMarkdown>
            </li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  )
}

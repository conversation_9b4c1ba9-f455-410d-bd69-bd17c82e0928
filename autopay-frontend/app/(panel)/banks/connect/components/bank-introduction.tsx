import { Alert, AlertDescription } from '@/components/ui/alert'
import type { BankConfig } from '@/lib/types/banks'
import { MdOutlineTipsAndUpdates } from 'react-icons/md'
import ReactMarkdown from 'react-markdown'

export default function Component({ bank }: { bank: BankConfig | null }) {
  if (!bank) {
    return null
  }

  return (
    bank.ui_config.introduction && (
      <Alert>
        <AlertDescription className="text-muted-foreground flex gap-4">
          <MdOutlineTipsAndUpdates className="text-primary h-6 w-6 shrink-0" />
          <div className="font-normal">
            <ReactMarkdown>{bank.ui_config.introduction}</ReactMarkdown>
          </div>
        </AlertDescription>
      </Alert>
    )
  )
}

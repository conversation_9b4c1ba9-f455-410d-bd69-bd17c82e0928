<?php

return [
    'name' => 'Bank',
    /*
    |--------------------------------------------------------------------------
    | Supported Banks Configuration
    |--------------------------------------------------------------------------
    |
    | This array contains configuration for all supported banks.
    | Each bank must have a strategy class that implements BankStrategyInterface.
    |
    */
    'supported_banks' => [
        'ocb' => [
            'name' => 'OceanBank',
            'display_name' => 'Ngân hàng TMCP Phương Đông (OCB)',
            'strategy_class' => \Modules\Bank\Strategies\OcbStrategy::class,
            'is_active' => true,
            'connection_fields' => ['accountNumber', 'idCardNumber', 'phoneNumber'],
            'supports' => [
                'virtual_account' => true,
                'account_validation' => true,
                'transaction_history' => true,
                'transaction_sync' => true,
                'virtual_account_transactions' => true,
                'account_linking' => true,
                'balance_check' => false,
            ],
            'ui_config' => [
                'icon' => '/banks/ocb-icon.png',
                'speed' => '1-5 giây',
                'register' => ['Online'],
                'has_virtual_account' => true,
                'virtual_account_prefix' => 'ITG',
                'promotion' => '',
                'stability' => '99.99%',
                'introduction' => 'Ngân hàng thương mại cổ phần Phương Đông, còn được gọi Oricombank hay OCB, là một Ngân hàng thương mại cổ phần tại Việt Nam.',
                'supports_display' => [
                    [
                        'support' => 'Đồng bộ tiền vào tài khoản ảo (VA)',
                        'icon' => 'BsArrowDownRightCircle',
                    ],
                    [
                        'support' => 'Hỗ trợ tạo tài khoản ảo (VA)',
                        'icon' => 'VscAccount',
                    ],
                ],
                'notes' => [
                    '- Đồng bộ khi giao dịch vào **tài khoản ảo (VA)**',
                    '- Hỗ trợ tạo **tài khoản ảo (VA)**',
                    '- Tốc độ đồng bộ giao dịch từ 1 đến 5 giây',
                    '- ',
                    '- *Chưa hỗ trợ đồng bộ giao dịch nhận tiền vào trực tiếp tài khoản chính, quý khách cần nhận tiền qua VA*',
                    '- *Chưa hỗ trợ đồng bộ tiền ra*',
                    '- *Chưa hỗ trợ hiện số dư tài khoản*',
                ],
            ],
            'api_config' => [
                'username' => env('OCB_USERNAME'),
                'password' => env('OCB_PASSWORD'),
                'client_id' => env('OCB_CLIENT_ID'),
                'client_secret' => env('OCB_CLIENT_SECRET'),
                'virtual_account_prefix' => env('OCB_VIRTUAL_ACCOUNT_PREFIX'),
                'api_url' => env('OCB_API_URL', 'https://api.ocb.com.vn'),
            ],
        ],

        'mb' => [
            'name' => 'MBBank',
            'display_name' => 'Ngân hàng TMCP Quân đội (MBBank)',
            'strategy_class' => \Modules\Bank\Strategies\MbStrategy::class,
            'is_active' => true,
            'connection_fields' => ['accountNumber', 'accountName', 'idCardNumber', 'phoneNumber'],
            'supports' => [
                'virtual_account' => true,
                'account_validation' => true,
                'transaction_history' => true,
                'balance_check' => true,
                'transaction_sync' => true,
                'virtual_account_transactions' => true,
                'account_linking' => false,
            ],
            'ui_config' => [
                'icon' => '/banks/mb-icon.png',
                'speed' => '1-5 giây',
                'register' => ['Online'],
                'has_virtual_account' => true,
                'virtual_account_prefix' => '',
                'promotion' => '',
                'stability' => '99.99%',
                'introduction' => 'Ngân hàng Thương mại Cổ phần Quân đội, gọi tắt là Ngân hàng Quân đội, viết tắt là MB, là một ngân hàng thương mại cổ phần của Việt Nam, một doanh nghiệp trực thuộc Bộ Quốc phòng.',
                'supports_display' => [
                    [
                        'support' => 'Đồng bộ tiền vào, tiền ra tài khoản chính',
                        'icon' => 'FaArrowRightArrowLeft',
                    ],
                    [
                        'support' => 'Hỗ trợ tạo tài khoản ảo (VA)',
                        'icon' => 'VscAccount',
                    ],
                ],
                'notes' => [
                    '- Đồng bộ khi giao dịch vào **tài khoản chính** và **tài khoản ảo (VA)**',
                    '- Hỗ trợ đồng bộ **tiền vào, tiền ra**',
                    '- Hỗ trợ tạo **tài khoản ảo (VA)**',
                    '- Tốc độ đồng bộ giao dịch từ 1 đến 5 giây',
                    '- ',
                    '- *Chưa hỗ trợ hiện số dư tài khoản*',
                ],
            ],
            'api_config' => [
                'username' => env('MB_USERNAME'),
                'password' => env('MB_PASSWORD'),
                'virtual_account_prefix' => env('MB_VIRTUAL_ACCOUNT_PREFIX'),
                'api_url' => env('MB_API_URL', 'https://api.mbbank.com.vn'),
            ],
        ],

        'klb' => [
            'name' => 'KienLongBank',
            'display_name' => 'Ngân hàng TMCP Kiên Long',
            'strategy_class' => \Modules\Bank\Strategies\KlbStrategy::class,
            'is_active' => true,
            'connection_fields' => ['accountNumber'],
            'supports' => [
                'virtual_account' => true,
                'account_validation' => true,
                'account_linking' => true,
                'virtual_account_transactions' => true,
                'balance_check' => false,
                'transaction_history' => false,
                'transaction_sync' => false,
            ],
            'ui_config' => [
                'icon' => '/banks/klb-icon.png',
                'speed' => '1-5 giây',
                'register' => ['Online'],
                'virtual_account' => true,
                'virtual_account_prefix' => '',
                'promotion' => '',
                'stability' => '99.99%',
                'introduction' => 'Ngân hàng TMCP Kiên Long là một ngân hàng thương mại cổ phần tại Việt Nam.',
                'supports_display' => [
                    [
                        'support' => 'Đồng bộ tiền vào tài khoản ảo (VA)',
                        'icon' => 'BsArrowDownRightCircle',
                    ],
                    [
                        'support' => 'Hỗ trợ tạo tài khoản ảo (VA)',
                        'icon' => 'VscAccount',
                    ],
                ],
                'notes' => [
                    '- Đồng bộ khi giao dịch vào **tài khoản ảo (VA)**',
                    '- Hỗ trợ tạo **tài khoản ảo (VA)**',
                    '- Tốc độ đồng bộ giao dịch từ 1 đến 5 giây',
                    '- ',
                    '- *Chưa hỗ trợ đồng bộ giao dịch nhận tiền vào trực tiếp tài khoản chính, quý khách cần nhận tiền qua VA*',
                    '- *Chưa hỗ trợ đồng bộ tiền ra*',
                    '- *Chưa hỗ trợ hiện số dư tài khoản*',
                ],
            ],
            'api_config' => [
                'base_url' => env('KLB_BASE_URL', 'https://api.kienlongbank.co/pay'),
                'client_id' => env('KLB_CLIENT_ID'),
                'client_secret' => env('KLB_CLIENT_SECRET'),
                'secret_key' => env('KLB_SECRET_KEY'),
                'encrypt_key' => env('KLB_ENCRYPT_KEY'),
                'virtual_account_prefix' => env('KLB_VIRTUAL_ACCOUNT_PREFIX'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Definitions Configuration
    |--------------------------------------------------------------------------
    |
    | This array contains configuration for all available bank features.
    | Each feature has a name, title, description, and category.
    |
    */
    'feature_definitions' => [
        'virtual_account' => [
            'name' => 'virtual_account',
            'title' => 'Tài khoản ảo (VA)',
            'description' => 'Hỗ trợ tạo và quản lý tài khoản ảo',
            'category' => 'account_management',
        ],
        'account_validation' => [
            'name' => 'account_validation',
            'title' => 'Xác thực tài khoản',
            'description' => 'Xác thực thông tin tài khoản ngân hàng',
            'category' => 'validation',
        ],
        'transaction_history' => [
            'name' => 'transaction_history',
            'title' => 'Lịch sử giao dịch',
            'description' => 'Lấy lịch sử giao dịch từ ngân hàng',
            'category' => 'transaction',
        ],
        'transaction_sync' => [
            'name' => 'transaction_sync',
            'title' => 'Đồng bộ giao dịch',
            'description' => 'Đồng bộ giao dịch real-time từ ngân hàng',
            'category' => 'transaction',
        ],
        'virtual_account_transactions' => [
            'name' => 'virtual_account_transactions',
            'title' => 'Giao dịch tài khoản ảo',
            'description' => 'Theo dõi giao dịch qua tài khoản ảo',
            'category' => 'transaction',
        ],
        'account_linking' => [
            'name' => 'account_linking',
            'title' => 'Liên kết tài khoản',
            'description' => 'Liên kết tài khoản ngân hàng với hệ thống',
            'category' => 'account_management',
        ],
        'balance_check' => [
            'name' => 'balance_check',
            'title' => 'Kiểm tra số dư',
            'description' => 'Kiểm tra số dư tài khoản ngân hàng',
            'category' => 'account_management',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Connection Fields Configuration
    |--------------------------------------------------------------------------
    |
    | This array contains configuration for connection fields used by banks.
    | Each field has a name, title, description, and minimum length.
    |
    */
    'connection_fields' => [
        'accountNumber' => [
            'name' => 'accountNumber',
            'title' => 'Số tài khoản',
            'description' => 'Số tài khoản của bạn tại ngân hàng',
            'min_length' => 3,
        ],
        'accountName' => [
            'name' => 'accountName',
            'title' => 'Tên tài khoản',
            'description' => 'Tên tài khoản của bạn tại ngân hàng',
            'min_length' => 3,
        ],
        'idCardNumber' => [
            'name' => 'idCardNumber',
            'title' => 'CCCD/CMT',
            'description' => 'Số CCCD/CMT đã dùng để đăng ký tài khoản ngân hàng',
            'min_length' => 9,
        ],
        'phoneNumber' => [
            'name' => 'phoneNumber',
            'title' => 'Số điện thoại',
            'description' => 'Số điện thoại đã dùng để đăng ký tài khoản ngân hàng',
            'min_length' => 9,
        ],
    ],
];

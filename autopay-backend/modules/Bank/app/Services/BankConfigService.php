<?php

namespace Modules\Bank\Services;

/**
 * Bank Configuration Service
 *
 * Provides methods to access bank configuration data
 */
class BankConfigService
{
    /**
     * Get all supported banks configuration
     *
     * @param bool $activeOnly
     * @return array
     */
    public function getSupportedBanks(bool $activeOnly = true): array
    {
        $banksConfig = config('bank.supported_banks', []);

        if (!$activeOnly) {
            return $banksConfig;
        }

        return array_filter($banksConfig, function ($config) {
            return $config['is_active'] ?? false;
        });
    }

    /**
     * Get specific bank configuration
     *
     * @param string $bankCode
     * @return array|null
     */
    public function getBankConfig(string $bankCode): ?array
    {
        $banksConfig = config('bank.supported_banks', []);

        return $banksConfig[$bankCode] ?? null;
    }

    /**
     * Get connection fields configuration
     *
     * @return array
     */
    public function getConnectionFields(): array
    {
        return config('bank.connection_fields', []);
    }

    /**
     * Get connection fields for specific bank
     *
     * @param string $bankCode
     * @return array
     */
    public function getBankConnectionFields(string $bankCode): array
    {
        $bankConfig = $this->getBankConfig($bankCode);

        if (!$bankConfig) {
            return [];
        }

        $connectionFields = $this->getConnectionFields();
        $bankConnectionFields = [];

        foreach ($bankConfig['connection_fields'] as $fieldName) {
            if (isset($connectionFields[$fieldName])) {
                $bankConnectionFields[] = $connectionFields[$fieldName];
            }
        }

        return $bankConnectionFields;
    }

    /**
     * Check if bank supports a specific feature
     *
     * @param string $bankCode
     * @param string $feature
     * @return bool
     */
    public function bankSupportsFeature(string $bankCode, string $feature): bool
    {
        $bankConfig = $this->getBankConfig($bankCode);

        if (!$bankConfig) {
            return false;
        }

        return in_array($feature, $bankConfig['supports'] ?? []);
    }

    /**
     * Get feature definitions configuration
     *
     * @return array
     */
    public function getFeatureDefinitions(): array
    {
        return config('bank.feature_definitions', []);
    }

    /**
     * Get feature definition by name
     *
     * @param string $featureName
     * @return array|null
     */
    public function getFeatureDefinition(string $featureName): ?array
    {
        $definitions = $this->getFeatureDefinitions();

        return $definitions[$featureName] ?? null;
    }

    /**
     * Get supported features for a bank with their definitions
     *
     * @param string $bankCode
     * @return array
     */
    public function getBankSupportedFeatures(string $bankCode): array
    {
        $bankConfig = $this->getBankConfig($bankCode);

        if (!$bankConfig) {
            return [];
        }

        $supportedFeatures = $bankConfig['supports'] ?? [];
        $featureDefinitions = $this->getFeatureDefinitions();
        $result = [];

        foreach ($supportedFeatures as $feature) {
            if (isset($featureDefinitions[$feature])) {
                $result[] = $featureDefinitions[$feature];
            }
        }

        return $result;
    }

    /**
     * Get bank strategy class
     *
     * @param string $bankCode
     * @return string|null
     */
    public function getBankStrategyClass(string $bankCode): ?string
    {
        $bankConfig = $this->getBankConfig($bankCode);

        return $bankConfig['strategy_class'] ?? null;
    }

    /**
     * Get banks formatted for frontend
     *
     * @return array
     */
    public function getBanksForFrontend(): array
    {
        $banksConfig = $this->getSupportedBanks();
        $connectionFields = $this->getConnectionFields();
        $featureDefinitions = $this->getFeatureDefinitions();

        $banks = [];

        foreach ($banksConfig as $code => $config) {
            // Get supported features with their definitions
            $supportedFeatures = [];
            foreach ($config['supports'] ?? [] as $feature) {
                if (isset($featureDefinitions[$feature])) {
                    $supportedFeatures[] = $featureDefinitions[$feature];
                }
            }

            $banks[] = [
                'code' => $code,
                'name' => $config['name'],
                'display_name' => $config['display_name'],
                'icon' => $config['ui_config']['icon'] ?? '',
                'speed' => $config['ui_config']['speed'] ?? '',
                'register' => $config['ui_config']['register'] ?? [],
                'virtualAccountPrefix' => $config['ui_config']['virtual_account_prefix'] ?? '',
                'promotion' => $config['ui_config']['promotion'] ?? '',
                'stability' => $config['ui_config']['stability'] ?? '',
                'connectionFields' => $config['connection_fields'],
                'introduction' => $config['ui_config']['introduction'] ?? '',
                'supports' => $config['supports'] ?? [],
                'supportedFeatures' => $supportedFeatures,
                'supportsDisplay' => $config['ui_config']['supports_display'] ?? [],
                'notes' => $config['ui_config']['notes'] ?? [],
            ];
        }

        return [
            'banks' => $banks,
            'connectionFields' => array_values($connectionFields),
            'featureDefinitions' => $featureDefinitions,
        ];
    }
}
